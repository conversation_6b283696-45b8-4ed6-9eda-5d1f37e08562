/// <reference types="node" />
export type JSONValue = string | number | boolean | null | JSONValue[] | {
    [key: string]: JSONValue;
};
export interface JSONObject {
    [key: string]: JSONValue;
}
export type FromObjectValue = string | number | boolean | null | FromObjectValue[] | Buffer | Uint8Array | {
    [key: string]: FromObjectValue;
};
export interface LongStub {
    toString: () => string;
}
