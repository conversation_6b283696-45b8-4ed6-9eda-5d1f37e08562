{"version": 3, "file": "wrappers.js", "sourceRoot": "", "sources": ["../../typescript/src/wrappers.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,iDAAiD;AACjD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;AAEjC,mCAA+D;AAE/D,iCAA8B;AAkB9B,SAAgB,mBAAmB,CACjC,GAA4E;IAE5E,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE;QACvD,OAAO,IAAI,CAAC;KACb;IACD,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,KAAK,YAAY,UAAU,EAAE;QACjE,OAAO,IAAA,yBAAiB,EAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KACrC;IACD,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,EAAE;QACjC,IAAA,aAAM,EACJ,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,MAAM,EACrC,0FAA0F,GAAG,CAAC,KAAK,EAAE,CACtG,CAAC;QACF,OAAQ,GAAG,CAAC,KAAkB,CAAC,QAAQ,EAAE,CAAC;KAC3C;IACD,wEAAwE;IACxE,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QAChE,OAAO,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;KAC7B;IACD,OAAO,GAAG,CAAC,KAAK,CAAC;AACnB,CAAC;AArBD,kDAqBC;AAED,SAAgB,qBAAqB,CACnC,QAAgB,EAChB,IAAsC;IAEtC,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,OAAO;YACL,KAAK,EAAE,IAAI;SACZ,CAAC;KACH;IACD,IAAI,QAAQ,KAAK,6BAA6B,EAAE;QAC9C,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,gGAAgG,OAAO,IAAI,EAAE,CAC9G,CAAC;SACH;QACD,OAAO;YACL,KAAK,EAAE,IAAA,2BAAmB,EAAC,IAAI,CAAC;SACjC,CAAC;KACH;IACD,OAAO;QACL,KAAK,EAAE,IAAI;KACZ,CAAC;AACJ,CAAC;AAtBD,sDAsBC"}